"""
Linux Log Collection Agent

This is the main agent class that coordinates log collection from various
Linux sources and sends them to the ExLog dashboard API.
"""

import json
import logging
import os
import signal
import socket
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from config.config_manager import ConfigManager
from utils.logger import <PERSON><PERSON><PERSON><PERSON><PERSON>, PerformanceLogger, AuditLogger
from utils.buffer import TimedBuffer
from utils.api_client import LinuxExLogAPIClient
from utils.uuid_generator import generate_log_id


class LinuxLoggingAgent:
    """
    Main Linux log collection agent that coordinates all log collection activities.
    """
    
    def __init__(self, config_path: Optional[str] = None, enable_signals: bool = True):
        """
        Initialize the Linux Logging Agent.
        
        Args:
            config_path: Path to configuration file
            enable_signals: Whether to enable signal handlers (disable for service mode)
        """
        self.config_manager = ConfigManager(config_path)
        self.config = {}
        self.logger = None
        self.performance_logger = None
        self.audit_logger = None
        self.enable_signals = enable_signals
        
        # Collectors
        self.collectors = {}
        
        # API Client
        self.api_client = None
        
        # Threading and control
        self._running = False
        self._collection_thread = None
        self._stop_event = threading.Event()
        
        # Buffer for collected logs
        self._log_buffer = None
        
        # Statistics
        self.stats = {
            'start_time': None,
            'logs_collected': 0,
            'logs_processed': 0,
            'logs_sent': 0,
            'errors': 0,
            'last_collection': None,
            'collectors_status': {}
        }
        
        # Initialize the agent
        print(f"DEBUG: About to initialize agent, enable_signals={enable_signals}")
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the agent components."""
        print("DEBUG: Starting _initialize method")
        try:
            # Load configuration
            self.config = self.config_manager.load_config()

            # Set up logging
            service_mode = not self.enable_signals
            print(f"DEBUG: enable_signals={self.enable_signals}, service_mode={service_mode}")
            self.logger = LoggerSetup.setup_logging(self.config, service_mode)
            self.performance_logger = PerformanceLogger(self.logger)
            self.audit_logger = AuditLogger(self.logger, service_mode)
            
            self.logger.info("Linux Log Collection Agent initializing...")
            
            # Initialize API client
            self._initialize_api_client()
            
            # Initialize log buffer
            self._initialize_buffer()
            
            # Initialize collectors
            self._initialize_collectors()
            
            # Set up signal handlers if enabled
            if self.enable_signals:
                self._setup_signal_handlers()
            
            self.logger.info("Linux Log Collection Agent initialized successfully")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error initializing agent: {e}")
            else:
                print(f"Error initializing agent: {e}")
            raise
    
    def _initialize_api_client(self) -> None:
        """Initialize the ExLog API client."""
        api_config = self.config.get('exlog_api', {})
        
        if api_config.get('enabled', False):
            try:
                self.api_client = LinuxExLogAPIClient(api_config)
                self.logger.info("ExLog API client initialized")
            except Exception as e:
                self.logger.error(f"Error initializing API client: {e}")
                self.api_client = None
        else:
            self.logger.info("ExLog API client disabled in configuration")
    
    def _initialize_buffer(self) -> None:
        """Initialize the log buffer."""
        buffer_size = self.config.get('general', {}).get('buffer_size', 1000)
        max_age = self.config.get('exlog_api', {}).get('max_batch_wait_time', 30)
        
        # Buffer flush callback
        def flush_callback(logs: List[Dict[str, Any]]) -> None:
            self._process_logs(logs)
        
        self._log_buffer = TimedBuffer(
            max_size=buffer_size,
            max_age_seconds=max_age,
            flush_callback=flush_callback
        )
        
        self.logger.info(f"Log buffer initialized (size: {buffer_size}, max_age: {max_age}s)")
    
    def _initialize_collectors(self) -> None:
        """Initialize log collectors based on configuration."""
        collection_config = self.config.get('collection', {})
        
        # Import collectors
        try:
            from .collectors.syslog_collector import SyslogCollector
            from .collectors.auth_log_collector import AuthLogCollector
            from .collectors.kernel_log_collector import KernelLogCollector
            from .collectors.journalctl_collector import JournalctlCollector
            from .collectors.application_log_collector import ApplicationLogCollector
        except ImportError as e:
            self.logger.error(f"Error importing collectors: {e}")
            return
        
        # Initialize syslog collector
        if collection_config.get('syslog', {}).get('enabled', False):
            try:
                self.collectors['syslog'] = SyslogCollector(
                    collection_config['syslog'],
                    self.logger
                )
                self.logger.info("Syslog collector initialized")
            except Exception as e:
                self.logger.error(f"Error initializing syslog collector: {e}")
        
        # Initialize auth log collector
        if collection_config.get('auth_logs', {}).get('enabled', False):
            try:
                self.collectors['auth'] = AuthLogCollector(
                    collection_config['auth_logs'],
                    self.logger
                )
                self.logger.info("Auth log collector initialized")
            except Exception as e:
                self.logger.error(f"Error initializing auth log collector: {e}")
        
        # Initialize kernel log collector
        if collection_config.get('kernel_logs', {}).get('enabled', False):
            try:
                self.collectors['kernel'] = KernelLogCollector(
                    collection_config['kernel_logs'],
                    self.logger
                )
                self.logger.info("Kernel log collector initialized")
            except Exception as e:
                self.logger.error(f"Error initializing kernel log collector: {e}")
        
        # Initialize journalctl collector
        if collection_config.get('journalctl', {}).get('enabled', False):
            try:
                self.collectors['journal'] = JournalctlCollector(
                    collection_config['journalctl'],
                    self.logger
                )
                self.logger.info("Journalctl collector initialized")
            except Exception as e:
                self.logger.error(f"Error initializing journalctl collector: {e}")
        
        # Initialize application log collector
        if collection_config.get('application_logs', {}).get('enabled', False):
            try:
                self.collectors['application'] = ApplicationLogCollector(
                    collection_config['application_logs'],
                    self.logger
                )
                self.logger.info("Application log collector initialized")
            except Exception as e:
                self.logger.error(f"Error initializing application log collector: {e}")
        
        self.logger.info(f"Initialized {len(self.collectors)} collectors")
    
    def _setup_signal_handlers(self) -> None:
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.stop()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self) -> bool:
        """
        Start the logging agent.
        
        Returns:
            True if started successfully, False otherwise
        """
        if self._running:
            self.logger.warning("Agent is already running")
            return True
        
        try:
            self._running = True
            self._stop_event.clear()
            self.stats['start_time'] = datetime.now()
            
            # Start log buffer
            if self._log_buffer:
                self._log_buffer.start()
            
            # Start API client
            if self.api_client:
                self.api_client.start()
            
            # Start collectors
            for name, collector in self.collectors.items():
                try:
                    if hasattr(collector, 'start'):
                        collector.start()
                    self.stats['collectors_status'][name] = 'running'
                    self.logger.debug(f"Started {name} collector")
                except Exception as e:
                    self.logger.error(f"Error starting {name} collector: {e}")
                    self.stats['collectors_status'][name] = 'error'
            
            # Start main collection thread
            self._collection_thread = threading.Thread(
                target=self._collection_loop,
                daemon=True
            )
            self._collection_thread.start()
            
            self.logger.info("Linux Log Collection Agent started successfully")
            self.audit_logger.log_service_start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting agent: {e}")
            self.stats['errors'] += 1
            return False
    
    def stop(self) -> None:
        """Stop the logging agent."""
        if not self._running:
            return
        
        self.logger.info("Stopping Linux Log Collection Agent...")
        self._running = False
        self._stop_event.set()
        
        # Stop collectors
        for name, collector in self.collectors.items():
            try:
                if hasattr(collector, 'stop'):
                    collector.stop()
                self.stats['collectors_status'][name] = 'stopped'
                self.logger.debug(f"Stopped {name} collector")
            except Exception as e:
                self.logger.error(f"Error stopping {name} collector: {e}")
        
        # Wait for collection thread to finish
        if self._collection_thread and self._collection_thread.is_alive():
            self._collection_thread.join(timeout=10)
        
        # Stop log buffer
        if self._log_buffer:
            self._log_buffer.stop()
        
        # Stop API client
        if self.api_client:
            self.api_client.stop()
        
        self.logger.info("Linux Log Collection Agent stopped")
        self.audit_logger.log_service_stop()
    
    def _collection_loop(self) -> None:
        """Main collection loop that runs in a separate thread."""
        processing_interval = self.config.get('general', {}).get('processing_interval', 5)
        
        self.logger.info(f"Collection loop started (interval: {processing_interval}s)")
        
        while self._running and not self._stop_event.is_set():
            try:
                start_time = time.time()
                
                # Collect logs from all enabled collectors
                self._collect_all_logs()
                
                # Update statistics
                self.stats['last_collection'] = datetime.now()
                
                # Performance monitoring
                self.performance_logger.log_memory_usage("LinuxLoggingAgent")
                
                # Calculate sleep time
                elapsed = time.time() - start_time
                sleep_time = max(0, processing_interval - elapsed)
                
                # Wait for next collection cycle or stop signal
                if sleep_time > 0:
                    self._stop_event.wait(sleep_time)
                
            except Exception as e:
                self.logger.error(f"Error in collection loop: {e}")
                self.stats['errors'] += 1
                
                # Wait before retrying
                self._stop_event.wait(5)
        
        self.logger.info("Collection loop stopped")
    
    def _collect_all_logs(self) -> None:
        """Collect logs from all enabled collectors."""
        total_collected = 0
        
        for name, collector in self.collectors.items():
            try:
                logs = collector.collect_logs()
                if logs:
                    # Add logs to buffer
                    self._log_buffer.add_many(logs)
                    total_collected += len(logs)
                    
                    self.logger.debug(f"Collected {len(logs)} logs from {name} collector")
                
            except Exception as e:
                self.logger.error(f"Error collecting logs from {name} collector: {e}")
                self.stats['errors'] += 1
                self.stats['collectors_status'][name] = 'error'
        
        if total_collected > 0:
            self.stats['logs_collected'] += total_collected
            self.logger.debug(f"Total logs collected this cycle: {total_collected}")
    
    def _process_logs(self, logs: List[Dict[str, Any]]) -> None:
        """Process a batch of logs (called by buffer flush callback)."""
        if not logs:
            return
        
        try:
            # Standardize logs
            standardized_logs = []
            for log in logs:
                standardized_log = self._standardize_log(log)
                if standardized_log:
                    standardized_logs.append(standardized_log)
            
            if not standardized_logs:
                return
            
            self.stats['logs_processed'] += len(standardized_logs)
            
            # Send to API if enabled
            if self.api_client:
                if self.api_client.send_logs(standardized_logs):
                    self.stats['logs_sent'] += len(standardized_logs)
                    self.logger.debug(f"Sent {len(standardized_logs)} logs to API")
                else:
                    self.logger.warning(f"Failed to send {len(standardized_logs)} logs to API")
            
            # Write to file if enabled
            file_config = self.config.get('output', {}).get('file', {})
            if file_config.get('enabled', False):
                self._write_logs_to_file(standardized_logs)
            
        except Exception as e:
            self.logger.error(f"Error processing logs: {e}")
            self.stats['errors'] += 1
    
    def _standardize_log(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Standardize a log entry to the ExLog format (match Windows agent exactly)."""
        try:
            # Generate log ID if not present
            if 'log_id' not in log or not log['log_id']:
                log_id_config = self.config.get('standardization', {}).get('log_id', {})
                format_type = log_id_config.get('format', 'uuid4')
                namespace = log_id_config.get('namespace')

                log['log_id'] = generate_log_id(log, format_type, namespace)

            # Format timestamp to match Windows agent (no Z suffix)
            timestamp = log.get('timestamp', datetime.now().strftime('%Y-%m-%dT%H:%M:%S'))
            if isinstance(timestamp, str):
                # Remove Z suffix and timezone info to match Windows format
                if timestamp.endswith('Z'):
                    timestamp = timestamp[:-1]
                if '+' in timestamp:
                    timestamp = timestamp.split('+')[0]
                # Ensure it's in the right format
                if 'T' not in timestamp:
                    timestamp = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

            # Ensure required fields are present (match Windows agent structure)
            standardized_log = {
                'log_id': log.get('log_id'),
                'timestamp': timestamp,
                'source': log.get('source', 'System'),
                'source_type': log.get('source_type', 'event'),  # Use 'event' like Windows
                'host': log.get('host', socket.gethostname()),  # Use actual hostname
                'log_level': log.get('log_level', 'info'),
                'message': log.get('message', ''),
                'raw_data': log.get('raw_data'),
                'additional_fields': log.get('additional_fields', {})
            }

            # Add metadata if configured (match Windows agent metadata structure)
            if self.config.get('standardization', {}).get('add_source_metadata', True):
                metadata = standardized_log['additional_fields'].get('metadata', {})
                metadata.update({
                    'collection_time': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f'),
                    'agent_version': '1.0.0',
                    'standardizer_version': '1.0.0',
                    'linux_agent': True,
                    'event_log_source': standardized_log['source']
                })
                standardized_log['additional_fields']['metadata'] = metadata

            return standardized_log

        except Exception as e:
            self.logger.error(f"Error standardizing log: {e}")
            return None
    
    def _write_logs_to_file(self, logs: List[Dict[str, Any]]) -> None:
        """Write logs to output file (match Windows agent format)."""
        try:
            file_config = self.config.get('output', {}).get('file', {})
            if not file_config.get('enabled', False):
                return

            output_path = file_config.get('path', '/var/log/linux-log-agent/standardized_logs.json')

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Write logs in the same format as Windows agent (one JSON object per line)
            with open(output_path, 'a', encoding='utf-8') as f:
                for log in logs:
                    # Write each log as a single line JSON (same as Windows agent)
                    json_line = json.dumps(log, separators=(',', ':'))
                    f.write(json_line + '\n')

            self.logger.debug(f"Wrote {len(logs)} logs to file: {output_path}")

        except Exception as e:
            self.logger.error(f"Error writing logs to file: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        status = {
            'running': self._running,
            'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None,
            'uptime_seconds': (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0,
            'statistics': self.stats.copy(),
            'collectors': self.stats['collectors_status'].copy(),
            'buffer_size': self._log_buffer.size() if self._log_buffer else 0,
            'api_client_stats': self.api_client.get_stats() if self.api_client else {}
        }
        
        return status
    
    def reload_config(self) -> bool:
        """Reload configuration from file."""
        try:
            self.config = self.config_manager.reload_config()
            self.logger.info("Configuration reloaded successfully")
            self.audit_logger.log_config_change(self.config_manager.config_path)
            return True
        except Exception as e:
            self.logger.error(f"Error reloading configuration: {e}")
            return False
