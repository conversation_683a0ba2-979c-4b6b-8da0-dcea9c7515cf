"""
Journalctl Collector for Linux Log Collection Agent

This module collects logs from systemd journal using journalctl command
on systemd-based Linux systems.
"""

import json
import logging
import subprocess
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .base_collector import BaseLogCollector


class JournalctlCollector(BaseLogCollector):
    """Collector for systemd journal logs via journalctl."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the journalctl collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        super().__init__(config, logger)
        
        # Journalctl-specific configuration
        self.units = config.get('units', [])  # Empty list means all units
        self.since = config.get('since', '1 hour ago')
        self.follow = config.get('follow', True)
        
        # Check if journalctl is available
        self.journalctl_available = self._check_journalctl_available()
        
        # For following logs in real-time
        self._follow_process = None
        self._follow_thread = None
        self._running = False
        
        # Track last cursor for incremental reading
        self._last_cursor = None
        
        self.logger.info(f"Journalctl collector initialized (available: {self.journalctl_available})")
    
    def _check_journalctl_available(self) -> bool:
        """Check if journalctl command is available."""
        try:
            result = subprocess.run(['journalctl', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def start(self) -> None:
        """Start the journalctl collector."""
        super().start()
        
        if not self.journalctl_available:
            self.logger.warning("journalctl not available, collector will not function")
            return
        
        self._running = True
        
        # Start following logs if enabled
        if self.follow:
            self._follow_thread = threading.Thread(target=self._follow_logs, daemon=True)
            self._follow_thread.start()
            self.logger.info("Started following journal logs in real-time")
    
    def stop(self) -> None:
        """Stop the journalctl collector."""
        super().stop()

        self._running = False

        # Stop following process more aggressively
        if self._follow_process:
            try:
                # First try to terminate gracefully
                self._follow_process.terminate()
                try:
                    self._follow_process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    # If it doesn't respond, kill it
                    self.logger.debug("Journalctl process didn't terminate, killing it")
                    self._follow_process.kill()
                    try:
                        self._follow_process.wait(timeout=1)
                    except subprocess.TimeoutExpired:
                        self.logger.warning("Journalctl process didn't respond to kill signal")
            except Exception as e:
                self.logger.error(f"Error stopping follow process: {e}")
            finally:
                self._follow_process = None

        # Wait for follow thread to finish with shorter timeout
        if self._follow_thread and self._follow_thread.is_alive():
            self._follow_thread.join(timeout=2)
            if self._follow_thread.is_alive():
                self.logger.warning("Journalctl follow thread didn't stop within timeout")

        self.logger.info("Journalctl collector stopped")
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from systemd journal.
        
        Returns:
            List of collected log entries
        """
        if not self.journalctl_available:
            return []
        
        collected_logs = []
        
        try:
            # Get logs since last cursor or since configured time
            logs = self._get_journal_logs()
            
            for log_data in logs:
                log_entry = self._parse_journal_entry(log_data)
                if log_entry:
                    collected_logs.append(log_entry)
            
            if collected_logs:
                self.stats['logs_collected'] += len(collected_logs)
                self.stats['last_collection'] = datetime.now()
                self.logger.debug(f"Collected {len(collected_logs)} journal entries")
            
        except Exception as e:
            self.logger.error(f"Error in journal log collection: {e}")
            self.stats['errors'] += 1
        
        return collected_logs
    
    def _get_journal_logs(self) -> List[Dict[str, Any]]:
        """Get logs from journalctl command."""
        cmd = ['journalctl', '--output=json', '--no-pager']
        
        # Add unit filters if specified
        if self.units:
            for unit in self.units:
                cmd.extend(['--unit', unit])
        
        # Add time filter
        if self._last_cursor:
            cmd.extend(['--after-cursor', self._last_cursor])
        else:
            cmd.extend(['--since', self.since])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                self.logger.error(f"journalctl command failed: {result.stderr}")
                return []
            
            # Parse JSON output
            logs = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        log_data = json.loads(line)
                        logs.append(log_data)
                        
                        # Update cursor
                        if '__CURSOR' in log_data:
                            self._last_cursor = log_data['__CURSOR']
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse journal JSON: {e}")
            
            return logs
            
        except subprocess.TimeoutExpired:
            self.logger.error("journalctl command timed out")
            return []
        except Exception as e:
            self.logger.error(f"Error running journalctl: {e}")
            return []
    
    def _follow_logs(self) -> None:
        """Follow journal logs in real-time."""
        cmd = ['journalctl', '--output=json', '--no-pager', '--follow']
        
        # Add unit filters if specified
        if self.units:
            for unit in self.units:
                cmd.extend(['--unit', unit])
        
        try:
            self._follow_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.logger.debug("Started journalctl follow process")
            
            # Read lines from the process with timeout
            import select
            while self._running and self._follow_process.poll() is None:
                try:
                    # Use select to check if data is available with timeout
                    ready, _, _ = select.select([self._follow_process.stdout], [], [], 0.5)

                    if ready:
                        line = self._follow_process.stdout.readline()
                        if line:
                            try:
                                log_data = json.loads(line.strip())
                                log_entry = self._parse_journal_entry(log_data)
                                if log_entry:
                                    # Add to buffer (this will be processed by the main agent)
                                    # For now, we'll just count it
                                    self.stats['logs_collected'] += 1
                            except json.JSONDecodeError:
                                continue
                    else:
                        # No data available, check if we should continue
                        if not self._running:
                            break
                        time.sleep(0.1)

                except Exception as e:
                    if self._running:
                        self.logger.error(f"Error reading from journalctl follow: {e}")
                    break
            
        except Exception as e:
            self.logger.error(f"Error in follow logs: {e}")
        finally:
            if self._follow_process:
                try:
                    self._follow_process.terminate()
                except Exception:
                    pass
    
    def _parse_journal_entry(self, log_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse a systemd journal entry.
        
        Args:
            log_data: Raw journal entry data
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        try:
            # Extract basic fields
            message = log_data.get('MESSAGE', '')
            if not message:
                return None
            
            # Convert timestamp
            timestamp = self._convert_journal_timestamp(log_data.get('__REALTIME_TIMESTAMP'))
            
            # Determine source and source type based on content and systemd unit
            source, source_type = self._categorize_journal_entry(log_data)
            
            # Determine log level
            log_level = self._convert_journal_priority(log_data.get('PRIORITY'))
            
            # Extract hostname
            hostname = log_data.get('_HOSTNAME', self.hostname)
            
            # Build additional fields
            additional_fields = {
                'systemd_unit': log_data.get('_SYSTEMD_UNIT'),
                'syslog_identifier': log_data.get('SYSLOG_IDENTIFIER'),
                'pid': self._safe_int(log_data.get('_PID')),
                'uid': self._safe_int(log_data.get('_UID')),
                'gid': self._safe_int(log_data.get('_GID')),
                'comm': log_data.get('_COMM'),
                'exe': log_data.get('_EXE'),
                'cmdline': log_data.get('_CMDLINE'),
                'systemd_cgroup': log_data.get('_SYSTEMD_CGROUP'),
                'systemd_slice': log_data.get('_SYSTEMD_SLICE'),
                'boot_id': log_data.get('_BOOT_ID'),
                'machine_id': log_data.get('_MACHINE_ID'),
                'cursor': log_data.get('__CURSOR'),
                'collector': 'journal'
            }
            
            # Remove None values
            additional_fields = {k: v for k, v in additional_fields.items() if v is not None}
            
            # Add systemd-specific information
            if log_data.get('_SYSTEMD_UNIT'):
                systemd_info = self._extract_systemd_info(log_data)
                if systemd_info:
                    additional_fields.update(systemd_info)
            
            log_entry = {
                'timestamp': timestamp,
                'source': source,
                'source_type': source_type,
                'host': hostname,
                'log_level': log_level,
                'message': self._sanitize_message(message),
                'raw_data': None,  # Don't include raw data to save space
                'additional_fields': additional_fields
            }
            
            return log_entry
            
        except Exception as e:
            self.logger.error(f"Error parsing journal entry: {e}")
            return None

    def _categorize_journal_entry(self, log_data: Dict[str, Any]) -> tuple:
        """
        Categorize journal entry based on content and systemd unit.

        Returns:
            Tuple of (source, source_type)
        """
        message = log_data.get('MESSAGE', '').lower()
        syslog_identifier = log_data.get('SYSLOG_IDENTIFIER', '').lower()
        systemd_unit = log_data.get('_SYSTEMD_UNIT', '').lower()
        comm = log_data.get('_COMM', '').lower()

        # Authentication logs
        if (syslog_identifier in ['sudo', 'su', 'login', 'sshd', 'gdm', 'lightdm', 'pam'] or
            'authentication' in message or 'login' in message or 'logout' in message or
            'sudo' in message or 'su:' in message or 'pam_' in message):
            return 'Auth', 'auth'

        # Kernel logs
        if (syslog_identifier == 'kernel' or comm == 'kernel' or
            systemd_unit.startswith('kernel') or
            any(kernel_word in message for kernel_word in [
                'kernel:', 'oops', 'panic', 'segfault', 'hardware', 'driver',
                'usb', 'pci', 'acpi', 'cpu', 'memory error', 'disk error'
            ])):
            return 'Kernel', 'kernel'

        # Network logs
        if (any(net_word in syslog_identifier for net_word in ['network', 'dhcp', 'dns', 'wifi', 'ethernet']) or
            any(net_word in message for net_word in [
                'network', 'interface', 'dhcp', 'dns', 'wifi', 'ethernet', 'ip address'
            ])):
            return 'Network', 'network'

        # Application logs (web servers, databases, etc.)
        if (any(app in syslog_identifier for app in [
                'apache', 'nginx', 'httpd', 'mysql', 'postgresql', 'redis', 'mongodb'
            ]) or
            any(app in systemd_unit for app in [
                'apache', 'nginx', 'httpd', 'mysql', 'postgresql', 'redis', 'mongodb'
            ])):
            return 'Application', 'application'

        # Security logs
        if (any(sec_word in message for sec_word in [
                'security', 'firewall', 'iptables', 'selinux', 'apparmor', 'fail2ban'
            ]) or
            syslog_identifier in ['fail2ban', 'iptables', 'ufw']):
            return 'Security', 'security'

        # System service logs
        if (systemd_unit and systemd_unit.endswith('.service') and
            systemd_unit not in ['<EMAIL>', 'session-1.scope']):
            return 'Service', 'service'

        # Cron/scheduled tasks
        if (syslog_identifier in ['cron', 'crond', 'anacron'] or
            'cron' in message or systemd_unit.endswith('.timer')):
            return 'Scheduler', 'scheduler'

        # Hardware logs
        if (any(hw_word in message for hw_word in [
                'hardware', 'usb', 'pci', 'bluetooth', 'audio', 'video', 'graphics'
            ]) or
            syslog_identifier in ['bluetoothd', 'pulseaudio', 'alsa']):
            return 'Hardware', 'hardware'

        # Default categorization based on systemd unit or syslog identifier
        if systemd_unit:
            return 'Systemd', 'systemd'
        elif syslog_identifier:
            return 'System', 'syslog'
        else:
            return 'Journal', 'journal'
    
    def _convert_journal_timestamp(self, timestamp_str: Optional[str]) -> str:
        """Convert journal timestamp to ISO format."""
        if not timestamp_str:
            return datetime.now().isoformat()
        
        try:
            # Journal timestamps are in microseconds since epoch
            timestamp_us = int(timestamp_str)
            timestamp_s = timestamp_us / 1000000
            dt = datetime.fromtimestamp(timestamp_s)
            return dt.isoformat()
        except (ValueError, TypeError):
            return datetime.now().isoformat()
    
    def _convert_journal_priority(self, priority: Optional[str]) -> str:
        """Convert journal priority to log level."""
        if not priority:
            return 'info'
        
        try:
            priority_int = int(priority)
            
            # Syslog priority levels
            if priority_int <= 2:  # Emergency, Alert, Critical
                return 'critical'
            elif priority_int == 3:  # Error
                return 'error'
            elif priority_int == 4:  # Warning
                return 'warning'
            elif priority_int <= 6:  # Notice, Info
                return 'info'
            else:  # Debug
                return 'debug'
                
        except (ValueError, TypeError):
            return 'info'
    
    def _safe_int(self, value: Any) -> Optional[int]:
        """Safely convert value to int."""
        if value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    
    def _extract_systemd_info(self, log_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract systemd-specific information."""
        systemd_info = {}
        
        # Unit state information
        if log_data.get('UNIT'):
            systemd_info['unit'] = log_data['UNIT']
        
        # Service information
        if log_data.get('_SYSTEMD_USER_UNIT'):
            systemd_info['user_unit'] = log_data['_SYSTEMD_USER_UNIT']
        
        # Invocation ID
        if log_data.get('_SYSTEMD_INVOCATION_ID'):
            systemd_info['invocation_id'] = log_data['_SYSTEMD_INVOCATION_ID']
        
        return systemd_info if systemd_info else None
    
    def get_stats(self) -> Dict[str, Any]:
        """Get journalctl collector statistics."""
        stats = super().get_stats()
        stats['collector_type'] = 'journal'
        stats['journalctl_available'] = self.journalctl_available
        stats['following'] = self.follow and self._running
        stats['units_filter'] = self.units
        stats['last_cursor'] = self._last_cursor
        return stats
