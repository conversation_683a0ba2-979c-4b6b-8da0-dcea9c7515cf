# Linux Log Collection Agent

A comprehensive, production-ready log collection agent for Linux systems that collects, standardizes, and forwards logs to the ExLog dashboard API with intelligent categorization and real-time monitoring.

## 🚀 Features

- **🔄 Real-time Log Collection**: Efficient real-time monitoring using systemd journal and inotify
- **🏷️ Intelligent Categorization**: Automatically categorizes logs into 9+ categories (Auth, Kernel, Network, etc.)
- **📡 API Integration**: Robust API client with retry logic, offline buffering, and batch processing
- **⚡ High Performance**: Optimized for minimal resource usage with configurable limits
- **🔒 Security Hardened**: Runs with minimal privileges and systemd security features
- **📊 Monitoring & Metrics**: Built-in performance monitoring and comprehensive statistics
- **🛠️ Production Ready**: Systemd service integration with proper error handling and logging
- **🔧 Configurable**: Extensive configuration options for different environments

## 📋 Requirements

- **OS**: Linux with systemd (Ubuntu 18.04+, Debian 10+, CentOS 7+, RHEL 7+, Fedora 30+)
- **Python**: 3.8 or higher
- **Permissions**: Root access for installation, runs as dedicated user
- **Dependencies**: Automatically installed by setup script

## 🚀 Quick Start

### Automated Installation (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd linux-log-agent

# Run the installation script
sudo ./install.sh

# The service will start automatically
sudo systemctl status linux-log-agent
```

### Configuration

Edit the configuration file to match your environment:

```bash
sudo nano /etc/linux-log-agent/config.yaml
```

Key settings to update:
```yaml
exlog_api:
  endpoint: "http://your-exlog-server:5000/api/v1/logs"
  api_key: "your-api-key-here"
```

Restart the service after configuration changes:
```bash
sudo systemctl restart linux-log-agent
```

## 📊 Log Categories

The agent intelligently categorizes logs into the following types:

| Category | Description | Examples |
|----------|-------------|----------|
| **Auth** | Authentication & authorization | SSH logins, sudo commands, PAM events |
| **Kernel** | Kernel & hardware messages | Hardware errors, driver events, system calls |
| **Network** | Network-related events | Interface changes, DHCP, DNS, connectivity |
| **Application** | Application logs | Web servers, databases, custom applications |
| **Security** | Security events | Firewall, SELinux, intrusion detection |
| **Service** | System service management | Service starts/stops, systemd events |
| **Scheduler** | Scheduled tasks | Cron jobs, systemd timers |
| **Hardware** | Hardware events | USB devices, Bluetooth, audio/video |
| **Systemd** | System management | Service control, system state changes |

## 🔧 Configuration

### Main Configuration File: `/etc/linux-log-agent/config.yaml`

<details>
<summary>Click to expand configuration sections</summary>

#### API Configuration
```yaml
exlog_api:
  enabled: true
  endpoint: "http://192.168.1.100:5000/api/v1/logs"
  api_key: "your-secure-api-key"
  batch_size: 100
  timeout: 30
  max_retries: 3
  offline_buffer:
    enabled: true
    max_size: 10000
```

#### Collection Sources
```yaml
collection:
  journalctl:
    enabled: true
    follow: true
    since: "1 hour ago"
  application_logs:
    enabled: true
    paths:
      - /var/log/apache2/
      - /var/log/nginx/
      - /var/log/mysql/
```

#### Performance Settings
```yaml
performance:
  max_cpu_percent: 10
  max_memory_mb: 256
  worker_threads: 2
```
</details>

## 🛠️ Management

### Service Commands
```bash
# Service status and control
sudo systemctl status linux-log-agent
sudo systemctl start linux-log-agent
sudo systemctl stop linux-log-agent
sudo systemctl restart linux-log-agent

# View real-time logs
sudo journalctl -u linux-log-agent -f

# Check service health
sudo systemctl is-active linux-log-agent
```

### Console Mode (Development/Testing)
```bash
cd /opt/linux-log-agent
sudo ./venv/bin/python main.py console --config /etc/linux-log-agent/config.yaml
```

## 📁 File Locations

| Purpose | Location |
|---------|----------|
| **Installation** | `/opt/linux-log-agent/` |
| **Configuration** | `/etc/linux-log-agent/config.yaml` |
| **Service Logs** | `/var/log/linux-log-agent/service.log` |
| **Agent Logs** | `/var/log/linux-log-agent/agent.log` |
| **Error Logs** | `/var/log/linux-log-agent/errors.log` |
| **Collected Logs** | `/var/log/linux-log-agent/standardized_logs.json` |
| **Systemd Service** | `/etc/systemd/system/linux-log-agent.service` |

## 🔍 Monitoring & Troubleshooting

### Health Checks
```bash
# Check if logs are being collected
sudo tail -f /var/log/linux-log-agent/standardized_logs.json

# Monitor service performance
sudo systemctl status linux-log-agent

# Check for errors
sudo tail -f /var/log/linux-log-agent/errors.log
```

### Common Issues & Solutions

<details>
<summary>Permission Issues</summary>

```bash
# Fix file permissions
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /var/log/linux-log-agent

# Add user to required groups
sudo usermod -a -G adm,systemd-journal,syslog linux-log-agent
```
</details>

<details>
<summary>Service Won't Start</summary>

```bash
# Check detailed status
sudo systemctl status linux-log-agent -l

# View recent logs
sudo journalctl -u linux-log-agent -n 50

# Check configuration syntax
sudo -u linux-log-agent /opt/linux-log-agent/venv/bin/python -c "import yaml; yaml.safe_load(open('/etc/linux-log-agent/config.yaml'))"
```
</details>

<details>
<summary>API Connection Issues</summary>

```bash
# Test API connectivity
curl -H "Authorization: Bearer YOUR_API_KEY" http://your-server:5000/api/v1/logs

# Check network configuration
sudo netstat -tlnp | grep :5000

# Review API errors
sudo grep -i "api" /var/log/linux-log-agent/errors.log
```
</details>

## 🔒 Security Features

- **Minimal Privileges**: Runs as dedicated `linux-log-agent` user
- **Systemd Hardening**:
  - `NoNewPrivileges=true`
  - `PrivateTmp=true`
  - `ProtectSystem=strict`
  - `ProtectHome=true`
- **Resource Limits**: CPU and memory usage limits
- **Secure File Access**: Read-only access to log directories

## 📈 Performance

- **Memory Usage**: ~30-100MB typical, 256MB limit
- **CPU Usage**: <10% on average
- **Log Processing**: 1000+ logs/second capability
- **Network Efficiency**: Batched API requests with compression

## 🔄 Recent Updates

See [CHANGELOG.md](CHANGELOG.md) for detailed version history and [FIXES.md](FIXES.md) for recent bug fixes and improvements.

### Latest Version Highlights:
- ✅ Enhanced log categorization with 9+ categories
- ✅ Improved systemd service shutdown handling
- ✅ Fixed installation dependencies and permissions
- ✅ Added comprehensive error handling and logging
- ✅ Optimized performance and resource usage

## 📚 Documentation

- [Installation Guide](docs/INSTALLATION.md) - Detailed installation instructions
- [Configuration Guide](docs/CONFIGURATION.md) - Complete configuration reference
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md) - Common issues and solutions
- [API Reference](docs/API.md) - ExLog API integration details
- [Development Guide](docs/DEVELOPMENT.md) - Development and contribution guidelines

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Logs**: Always include relevant log excerpts when reporting issues
- **Community**: Join our discussions for help and best practices

---

**Made with ❤️ for reliable Linux log collection**
