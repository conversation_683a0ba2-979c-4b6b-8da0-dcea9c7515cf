# Linux Log Collection Agent Dependencies
#
# IMPORTANT: For Ubuntu 22.04+ and other distributions with PEP 668 enforcement,
# use one of these installation methods:
#
# Method 1 (Recommended): Use the setup script
#   ./setup_dev.sh
#
# Method 2: Create virtual environment manually
#   python3 -m venv venv
#   source venv/bin/activate
#   pip install -r requirements.txt
#
# Method 3: Install system packages (Ubuntu/Debian)
#   sudo apt install python3-requests python3-yaml python3-psutil python3-systemd python3-dateutil python3-tz
#
# Method 4: Override protection (NOT RECOMMENDED)
#   pip install -r requirements.txt --break-system-packages

# Core dependencies
requests>=2.25.0
PyYAML>=5.4.0
psutil>=5.8.0

# Linux-specific dependencies
systemd-python>=234
inotify>=0.2.10

# Optional dependencies for enhanced functionality
python-dateutil>=2.8.0
pytz>=2021.1

# Development and testing dependencies (optional)
pytest>=6.0.0
pytest-cov>=2.10.0
