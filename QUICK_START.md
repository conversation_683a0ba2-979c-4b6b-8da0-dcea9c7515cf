# Quick Start Guide - Linux Log Collection Agent

This guide helps you get the Linux Log Collection Agent running quickly, especially on Ubuntu 22.04+ and other distributions with PEP 668 enforcement.

## The Problem

If you're getting this error:
```
error: externally-managed-environment
× This environment is externally managed
```

This is because newer Linux distributions prevent installing Python packages system-wide to avoid conflicts.

## Solutions (Choose One)

### Option 1: Development Setup (Recommended for Testing)

```bash
cd linux-agent
./setup_dev.sh
```

This script will:
- Install system packages
- Create a virtual environment
- Install Python dependencies
- Create helper scripts

Then run:
```bash
# Test the agent
./run_dev.sh console --config config/default_config.yaml

# Test API connection
./run_dev.sh test_api_client.py
```

### Option 2: Production Installation

```bash
cd linux-agent
sudo ./install/install.sh
```

This installs the agent as a system service with proper virtual environment handling.

### Option 3: Manual Virtual Environment

```bash
cd linux-agent

# Create virtual environment
python3 -m venv venv

# Activate it
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the agent
python main.py console --config config/default_config.yaml
```

### Option 4: System Packages (Ubuntu/Debian)

```bash
# Install system packages instead
sudo apt update
sudo apt install python3-requests python3-yaml python3-psutil python3-systemd python3-dateutil python3-tz

# Run directly
python3 main.py console --config config/default_config.yaml
```

## Configuration

Before running, edit the configuration file:

### Development
```bash
# Edit the default config
nano config/default_config.yaml
```

### Production
```bash
# Edit the installed config
sudo nano /etc/linux-log-agent/config.yaml
```

Key settings to update:
```yaml
exlog_api:
  enabled: true
  endpoint: "http://your-dashboard-ip:5000/api/v1/logs"  # Update this
  api_key: "your-api-key-here"  # Get from dashboard
  batch_size: 100
  timeout: 30
```

## Testing

### Test API Connection
```bash
# Development
./run_dev.sh test_api_client.py

# Or with virtual environment
source venv/bin/activate
python test_api_client.py

# Or with system packages
python3 test_api_client.py
```

### Test Log Collection
```bash
# Development
./run_dev.sh console --config config/default_config.yaml

# Or with virtual environment
source venv/bin/activate
python main.py console --config config/default_config.yaml

# Or with system packages
sudo python3 main.py console --config config/default_config.yaml
```

## Troubleshooting

### Virtual Environment Issues
```bash
# Make sure python3-venv is installed
sudo apt install python3-venv python3-full

# Recreate virtual environment
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Permission Issues
```bash
# Make sure scripts are executable
chmod +x setup_dev.sh
chmod +x run_dev.sh
chmod +x activate_dev.sh
```

### Missing System Packages
```bash
# Ubuntu/Debian
sudo apt install python3-dev python3-venv python3-full

# CentOS/RHEL/Fedora
sudo dnf install python3-devel python3-venv

# SUSE
sudo zypper install python3-devel python3-venv
```

### API Connection Issues
1. Check if the dashboard is running: `curl http://dashboard-ip:5000/api/v1/health`
2. Verify API key in configuration
3. Check firewall settings
4. Ensure network connectivity

## Next Steps

1. **Configure the API endpoint** in your config file
2. **Get an API key** from your ExLog dashboard
3. **Test the connection** with the test script
4. **Run in console mode** to see logs being collected
5. **Install as service** for production use

## File Locations

### Development
- Configuration: `config/default_config.yaml`
- Virtual environment: `venv/`
- Run script: `./run_dev.sh`
- Activation script: `./activate_dev.sh`

### Production
- Configuration: `/etc/linux-log-agent/config.yaml`
- Installation: `/opt/linux-log-agent/`
- Logs: `/var/log/linux-log-agent/`
- Service: `systemctl status linux-log-agent`

## Getting Help

If you encounter issues:

1. Check the logs:
   - Development: Console output
   - Production: `sudo journalctl -u linux-log-agent -f`

2. Verify configuration:
   - Test API connection
   - Check file permissions
   - Verify log file paths

3. Check system requirements:
   - Python 3.7+
   - Required system packages
   - Network connectivity

For more detailed information, see the main README.md file.
