#!/bin/bash

# Linux Log Collection Agent Installation Script
# This script installs the Linux log collection agent as a systemd service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AGENT_USER="linux-log-agent"
AGENT_GROUP="linux-log-agent"
INSTALL_DIR="/opt/linux-log-agent"
CONFIG_DIR="/etc/linux-log-agent"
LOG_DIR="/var/log/linux-log-agent"
SERVICE_NAME="linux-log-agent"

# Functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

detect_distribution() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        print_error "Cannot detect Linux distribution"
        exit 1
    fi
    
    print_info "Detected distribution: $DISTRO $VERSION"
}

install_dependencies() {
    print_info "Installing dependencies..."

    case $DISTRO in
        ubuntu|debian)
            apt-get update
            apt-get install -y python3 python3-pip python3-venv python3-full systemd
            # Install build dependencies for systemd-python and other packages
            apt-get install -y pkg-config libsystemd-dev python3-dev build-essential
            # Install system packages for common Python dependencies
            apt-get install -y python3-requests python3-yaml python3-psutil python3-systemd python3-dateutil python3-tz || true
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                dnf install -y python3 python3-pip python3-venv systemd
                # Install build dependencies for systemd-python and other packages
                dnf install -y pkgconfig systemd-devel python3-devel gcc
                # Install system packages for common Python dependencies
                dnf install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz || true
            else
                yum install -y python3 python3-pip systemd
                # Install build dependencies for systemd-python and other packages
                yum install -y pkgconfig systemd-devel python3-devel gcc
                # Install system packages for common Python dependencies
                yum install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz || true
            fi
            ;;
        suse|opensuse*)
            zypper install -y python3 python3-pip python3-venv systemd
            # Install build dependencies for systemd-python and other packages
            zypper install -y pkg-config systemd-devel python3-devel gcc
            # Install system packages for common Python dependencies
            zypper install -y python3-requests python3-PyYAML python3-psutil python3-systemd python3-dateutil python3-pytz || true
            ;;
        *)
            print_warning "Unknown distribution, attempting to install with available package manager"
            if command -v apt-get &> /dev/null; then
                apt-get update && apt-get install -y python3 python3-pip python3-venv python3-full systemd
                apt-get install -y pkg-config libsystemd-dev python3-dev build-essential
                apt-get install -y python3-requests python3-yaml python3-psutil python3-systemd python3-dateutil python3-tz || true
            elif command -v dnf &> /dev/null; then
                dnf install -y python3 python3-pip python3-venv systemd
                dnf install -y pkgconfig systemd-devel python3-devel gcc
                dnf install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz || true
            elif command -v yum &> /dev/null; then
                yum install -y python3 python3-pip systemd
                yum install -y pkgconfig systemd-devel python3-devel gcc
                yum install -y python3-requests python3-pyyaml python3-psutil python3-systemd python3-dateutil pytz || true
            elif command -v zypper &> /dev/null; then
                zypper install -y python3 python3-pip python3-venv systemd
                zypper install -y pkg-config systemd-devel python3-devel gcc
                zypper install -y python3-requests python3-PyYAML python3-psutil python3-systemd python3-dateutil python3-pytz || true
            else
                print_error "No supported package manager found"
                exit 1
            fi
            ;;
    esac

    print_success "Dependencies installed"
}

create_user() {
    print_info "Creating user and group..."
    
    # Create group if it doesn't exist
    if ! getent group $AGENT_GROUP > /dev/null 2>&1; then
        groupadd --system $AGENT_GROUP
        print_info "Created group: $AGENT_GROUP"
    fi
    
    # Create user if it doesn't exist
    if ! getent passwd $AGENT_USER > /dev/null 2>&1; then
        useradd --system --gid $AGENT_GROUP --home-dir $INSTALL_DIR \
                --shell /bin/false --comment "Linux Log Agent" $AGENT_USER
        print_info "Created user: $AGENT_USER"
    fi
    
    # Add user to required groups for log access
    usermod -a -G adm $AGENT_USER 2>/dev/null || true
    usermod -a -G systemd-journal $AGENT_USER 2>/dev/null || true
    usermod -a -G syslog $AGENT_USER 2>/dev/null || true
    
    print_success "User and group configured"
}

create_directories() {
    print_info "Creating directories..."
    
    # Create installation directory
    mkdir -p $INSTALL_DIR
    
    # Create configuration directory
    mkdir -p $CONFIG_DIR
    
    # Create log directory
    mkdir -p $LOG_DIR
    
    # Set ownership
    chown -R $AGENT_USER:$AGENT_GROUP $INSTALL_DIR
    chown -R $AGENT_USER:$AGENT_GROUP $CONFIG_DIR
    chown -R $AGENT_USER:$AGENT_GROUP $LOG_DIR
    
    # Set permissions
    chmod 755 $INSTALL_DIR
    chmod 750 $CONFIG_DIR
    chmod 750 $LOG_DIR
    
    print_success "Directories created"
}

install_agent() {
    print_info "Installing agent files..."
    
    # Get the directory where this script is located
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    AGENT_SOURCE_DIR="$(dirname "$SCRIPT_DIR")"
    
    # Copy agent files
    cp -r "$AGENT_SOURCE_DIR"/* $INSTALL_DIR/
    
    # Remove installation directory from the copied files
    rm -rf $INSTALL_DIR/install
    
    # Set ownership
    chown -R $AGENT_USER:$AGENT_GROUP $INSTALL_DIR
    
    # Make main script executable
    chmod +x $INSTALL_DIR/main.py
    chmod +x $INSTALL_DIR/service/systemd_service.py
    
    print_success "Agent files installed"
}

install_python_dependencies() {
    print_info "Installing Python dependencies..."

    # Create virtual environment
    print_info "Creating virtual environment..."
    sudo -u $AGENT_USER python3 -m venv $INSTALL_DIR/venv

    # Install dependencies in virtual environment
    print_info "Installing packages in virtual environment..."
    sudo -u $AGENT_USER $INSTALL_DIR/venv/bin/pip install --upgrade pip
    sudo -u $AGENT_USER $INSTALL_DIR/venv/bin/pip install -r $INSTALL_DIR/requirements.txt

    # Create a wrapper script that uses the virtual environment
    cat > $INSTALL_DIR/run_agent.sh << 'EOF'
#!/bin/bash
# Wrapper script to run the agent with virtual environment
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
exec "$SCRIPT_DIR/venv/bin/python" "$SCRIPT_DIR/main.py" "$@"
EOF

    chmod +x $INSTALL_DIR/run_agent.sh
    chown $AGENT_USER:$AGENT_GROUP $INSTALL_DIR/run_agent.sh

    print_success "Python dependencies installed in virtual environment"
}

install_configuration() {
    print_info "Installing configuration..."
    
    # Copy default configuration if it doesn't exist
    if [ ! -f $CONFIG_DIR/config.yaml ]; then
        cp $INSTALL_DIR/config/default_config.yaml $CONFIG_DIR/config.yaml
        chown $AGENT_USER:$AGENT_GROUP $CONFIG_DIR/config.yaml
        chmod 640 $CONFIG_DIR/config.yaml
        print_info "Default configuration installed"
    else
        print_warning "Configuration file already exists, skipping"
    fi
    
    print_success "Configuration installed"
}

install_systemd_service() {
    print_info "Installing systemd service..."
    
    # Copy service file
    cp $INSTALL_DIR/install/systemd/linux-log-agent.service /etc/systemd/system/
    
    # Reload systemd
    systemctl daemon-reload
    
    # Enable service
    systemctl enable $SERVICE_NAME
    
    print_success "Systemd service installed and enabled"
}

configure_logrotate() {
    print_info "Configuring log rotation..."
    
    cat > /etc/logrotate.d/linux-log-agent << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 640 $AGENT_USER $AGENT_GROUP
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF
    
    print_success "Log rotation configured"
}

show_post_install_info() {
    print_success "Installation completed successfully!"
    echo
    print_info "Next steps:"
    echo "1. Edit the configuration file: $CONFIG_DIR/config.yaml"
    echo "2. Configure your ExLog API endpoint and key"
    echo "3. Start the service: sudo systemctl start $SERVICE_NAME"
    echo "4. Check service status: sudo systemctl status $SERVICE_NAME"
    echo "5. View logs: sudo journalctl -u $SERVICE_NAME -f"
    echo
    print_info "Service management commands:"
    echo "  Start:   sudo systemctl start $SERVICE_NAME"
    echo "  Stop:    sudo systemctl stop $SERVICE_NAME"
    echo "  Restart: sudo systemctl restart $SERVICE_NAME"
    echo "  Status:  sudo systemctl status $SERVICE_NAME"
    echo "  Logs:    sudo journalctl -u $SERVICE_NAME"
    echo
    print_info "Configuration file: $CONFIG_DIR/config.yaml"
    print_info "Log directory: $LOG_DIR"
    print_info "Installation directory: $INSTALL_DIR"
}

# Main installation process
main() {
    print_info "Starting Linux Log Collection Agent installation..."
    
    check_root
    detect_distribution
    install_dependencies
    create_user
    create_directories
    install_agent
    install_python_dependencies
    install_configuration
    install_systemd_service
    configure_logrotate
    
    show_post_install_info
}

# Run main function
main "$@"
