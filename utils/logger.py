"""
Logging utilities for Linux Log Collection Agent

This module provides logging setup and utilities for the Linux log collection agent.
"""

import logging
import logging.handlers
import os
import sys
import time
import psutil
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any


class LoggerSetup:
    """Sets up logging for the Linux log collection agent."""
    
    @staticmethod
    def setup_logging(config: Dict[str, Any], service_mode: bool = False) -> logging.Logger:
        """
        Set up logging configuration.
        
        Args:
            config: Configuration dictionary
            service_mode: Whether running as a service
            
        Returns:
            Configured logger instance
        """
        # Get logging configuration
        log_level = config.get('general', {}).get('log_level', 'INFO')
        
        # Create logs directory
        log_dir = '/var/log/linux-log-agent' if service_mode else 'logs'
        print(f"DEBUG: LoggerSetup service_mode={service_mode}, log_dir={log_dir}")
        os.makedirs(log_dir, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler
        log_file = os.path.join(log_dir, 'agent.log')
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Console handler (only if not in service mode)
        if not service_mode:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # Error file handler
        error_file = os.path.join(log_dir, 'errors.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        logger = logging.getLogger('LinuxLogAgent')
        logger.info("Logging initialized")
        
        return logger


class PerformanceLogger:
    """Logs performance metrics for the Linux log collection agent."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initialize performance logger."""
        self.logger = logger or logging.getLogger(__name__)
        self.process = psutil.Process()
        
    def log_memory_usage(self, component: str = "Agent") -> None:
        """Log current memory usage."""
        try:
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.logger.debug(f"{component} memory usage: {memory_mb:.2f} MB")
            
            # Log warning if memory usage is high
            if memory_mb > 100:  # More than 100MB
                self.logger.warning(f"{component} high memory usage: {memory_mb:.2f} MB")
                
        except Exception as e:
            self.logger.error(f"Error logging memory usage: {e}")
    
    def log_cpu_usage(self, component: str = "Agent") -> None:
        """Log current CPU usage."""
        try:
            cpu_percent = self.process.cpu_percent()
            
            self.logger.debug(f"{component} CPU usage: {cpu_percent:.2f}%")
            
            # Log warning if CPU usage is high
            if cpu_percent > 50:  # More than 50%
                self.logger.warning(f"{component} high CPU usage: {cpu_percent:.2f}%")
                
        except Exception as e:
            self.logger.error(f"Error logging CPU usage: {e}")
    
    def log_disk_usage(self, path: str = "/") -> None:
        """Log disk usage for a given path."""
        try:
            disk_usage = psutil.disk_usage(path)
            used_percent = (disk_usage.used / disk_usage.total) * 100
            
            self.logger.debug(f"Disk usage for {path}: {used_percent:.2f}%")
            
            # Log warning if disk usage is high
            if used_percent > 90:  # More than 90%
                self.logger.warning(f"High disk usage for {path}: {used_percent:.2f}%")
                
        except Exception as e:
            self.logger.error(f"Error logging disk usage for {path}: {e}")
    
    def log_system_stats(self) -> None:
        """Log comprehensive system statistics."""
        try:
            # Memory
            memory = psutil.virtual_memory()
            self.logger.info(f"System memory: {memory.percent}% used ({memory.used/1024/1024/1024:.2f}GB / {memory.total/1024/1024/1024:.2f}GB)")
            
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            self.logger.info(f"System CPU: {cpu_percent}%")
            
            # Load average (Linux specific)
            if hasattr(os, 'getloadavg'):
                load_avg = os.getloadavg()
                self.logger.info(f"Load average: {load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}")
            
            # Disk usage for root
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.logger.info(f"Root disk: {disk_percent:.2f}% used ({disk.used/1024/1024/1024:.2f}GB / {disk.total/1024/1024/1024:.2f}GB)")
            
        except Exception as e:
            self.logger.error(f"Error logging system stats: {e}")


class AuditLogger:
    """Logs audit events for the Linux log collection agent."""

    def __init__(self, logger: Optional[logging.Logger] = None, service_mode: bool = False):
        """Initialize audit logger."""
        self.logger = logger or logging.getLogger(__name__)

        # Create audit log file
        log_dir = '/var/log/linux-log-agent' if service_mode else 'logs'
        print(f"DEBUG: AuditLogger service_mode={service_mode}, log_dir={log_dir}")
        os.makedirs(log_dir, exist_ok=True)
        
        audit_file = os.path.join(log_dir, 'audit.log')
        self.audit_handler = logging.handlers.RotatingFileHandler(
            audit_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        
        audit_formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.audit_handler.setFormatter(audit_formatter)
        
        # Create audit logger
        self.audit_logger = logging.getLogger('audit')
        self.audit_logger.setLevel(logging.INFO)
        self.audit_logger.addHandler(self.audit_handler)
        self.audit_logger.propagate = False
    
    def log_service_start(self) -> None:
        """Log service start event."""
        self.audit_logger.info("Linux Log Collection Agent started")
    
    def log_service_stop(self) -> None:
        """Log service stop event."""
        self.audit_logger.info("Linux Log Collection Agent stopped")
    
    def log_config_change(self, config_path: str) -> None:
        """Log configuration change event."""
        self.audit_logger.info(f"Configuration changed: {config_path}")
    
    def log_api_connection(self, endpoint: str, success: bool) -> None:
        """Log API connection attempt."""
        status = "successful" if success else "failed"
        self.audit_logger.info(f"API connection {status}: {endpoint}")
    
    def log_log_file_access(self, file_path: str, success: bool) -> None:
        """Log log file access attempt."""
        status = "successful" if success else "failed"
        self.audit_logger.info(f"Log file access {status}: {file_path}")
    
    def log_permission_error(self, resource: str, error: str) -> None:
        """Log permission error."""
        self.audit_logger.warning(f"Permission error accessing {resource}: {error}")
    
    def log_error(self, component: str, error: str) -> None:
        """Log general error."""
        self.audit_logger.error(f"{component} error: {error}")


class FileLogger:
    """Handles file-based logging output."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize file logger."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Get file output configuration
        file_config = config.get('output', {}).get('file', {})
        self.enabled = file_config.get('enabled', False)
        
        if not self.enabled:
            return
        
        self.output_path = file_config.get('path', '/var/log/linux-log-agent/standardized_logs.json')
        
        # Create output directory
        os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
        
        # Set up rotation if enabled
        rotation_config = file_config.get('rotation', {})
        if rotation_config.get('enabled', False):
            max_size = self._parse_size(rotation_config.get('max_size', '100MB'))
            backup_count = rotation_config.get('backup_count', 5)
            
            self.file_handler = logging.handlers.RotatingFileHandler(
                self.output_path,
                maxBytes=max_size,
                backupCount=backup_count
            )
        else:
            self.file_handler = logging.FileHandler(self.output_path)
        
        # Set up formatter for JSON output
        self.file_handler.setFormatter(logging.Formatter('%(message)s'))
        
        # Create dedicated logger for file output
        self.file_logger = logging.getLogger('file_output')
        self.file_logger.setLevel(logging.INFO)
        self.file_logger.addHandler(self.file_handler)
        self.file_logger.propagate = False
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string (e.g., '100MB') to bytes."""
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def write_log(self, log_entry: Dict[str, Any]) -> None:
        """Write a log entry to file."""
        if not self.enabled:
            return
        
        try:
            import json
            log_line = json.dumps(log_entry, separators=(',', ':'))
            self.file_logger.info(log_line)
        except Exception as e:
            self.logger.error(f"Error writing log to file: {e}")
    
    def write_logs(self, log_entries: list) -> None:
        """Write multiple log entries to file."""
        if not self.enabled:
            return
        
        for log_entry in log_entries:
            self.write_log(log_entry)
